stages:
  - build
  - deploy

variables:
  SERVER_PORT: "80"
  DOCKER_IMAGE: "erp-backend:latest"
  DOCKER_CONTAINER_NAME: "backend"
  DOCKER_HOST_PORT: "8082"

# Stage de build (optionnel, pour les tests)
build:
  stage: build
  image: maven:3.9.6-eclipse-temurin-17
  cache:
    paths:
      - .m2/repository
  variables:
    MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"
  script:
    - echo "Building application..."
    - mvn clean compile -DskipTests


# Déploiement en production
deploy_prod:
  stage: deploy
  tags:
    - docker-socket
  variables:
    SPRING_PROFILES_ACTIVE: "prod"
  script:
    - echo "Deploying to production..."

    # Construction de l'image Docker
    - docker build -t $DOCKER_IMAGE .

    # Arrêt et suppression du conteneur existant
    - docker rm -f $DOCKER_CONTAINER_NAME || true

    # Création sécurisée du fichier .env
    - echo "Creating environment file..."
    - |
      {
        echo "SERVER_PORT=${SERVER_PORT:-80}"
        echo "SPRING_PROFILES_ACTIVE=${SPRING_PROFILES_ACTIVE:-prod}"
        echo "SECURITY_WHITE_LIST=${SECURITY_WHITE_LIST:-/,/v3/api-docs/**,/swagger-ui/**,/swagger,/doc,/docs,/api/auth/**}"
        echo "JWT_SECRET_KEY=${JWT_SECRET_KEY:-9CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED8E}"
        echo "JWT_DELAY=${JWT_DELAY:-24}"
        echo "DB_DGD_USERNAME=${DB_DGD_USERNAME:-root}"
        echo "DB_DGD_PASSWORD=${DB_DGD_PASSWORD:-}"
        echo "DB_DGD_URL=${DB_DGD_URL:-************************************}"
        echo "DB_HOME_USERNAME=${DB_HOME_USERNAME:-root}"
        echo "DB_HOME_PASSWORD=${DB_HOME_PASSWORD:-}"
        echo "DB_HOME_URL=${DB_HOME_URL:-*************************************}"
        echo "DB_TEST_USERNAME=${DB_TEST_USERNAME:-root}"
        echo "DB_TEST_PASSWORD=${DB_TEST_PASSWORD:-}"
        echo "DB_TEST_URL=${DB_TEST_URL:-*************************************}"
        echo "MAIL_HOST=${MAIL_HOST:-mail.finances.bj}"
        echo "MAIL_USERNAME=${MAIL_USERNAME:-<EMAIL>}"
        echo "MAIL_PASSWORD=${MAIL_PASSWORD:-}"
      } > .env

    # Vérification du fichier (sans afficher le contenu sensible)
    - echo "Environment file created with $(wc -l < .env) variables"

    # Démarrage du conteneur
    - |
      docker run -d \
        --restart=always \
        -p $DOCKER_HOST_PORT:$SERVER_PORT \
        --name $DOCKER_CONTAINER_NAME \
        --env-file .env \
        $DOCKER_IMAGE

    # Nettoyage immédiat du fichier .env
    - rm -f .env
    - echo "Environment file cleaned up"

    # Vérification du déploiement
    - echo "Waiting for application to start..."
    - sleep 10
    - docker logs $DOCKER_CONTAINER_NAME --tail 20

    # Test de santé (optionnel)
    - echo "Testing application health..."
    - curl -f http://localhost:$DOCKER_HOST_PORT/actuator/health || echo "Health check failed"

  only:
    - main
  environment:
    name: production
