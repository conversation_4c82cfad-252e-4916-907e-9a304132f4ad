stages:
  - build
  - deploy

variables:
  SERVER_PORT: "80"
  DOCKER_IMAGE: "erp-backend:latest"
  DOCKER_CONTAINER_NAME: "backend2"
  DOCKER_HOST_PORT: "8083"

# Stage de build (optionnel, pour les tests)
build:
  stage: build
  image: maven:3.9.6-eclipse-temurin-17
  cache:
    paths:
      - .m2/repository
  variables:
    MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"
  script:
    - echo "Building application..."
    - mvn clean compile -DskipTests


# Déploiement en production
deploy_prod:
  stage: deploy
  tags:
    - docker-socket
  variables:
    SPRING_PROFILES_ACTIVE: "prod"
    DB_DGD_PASSWORD: "rootoor"
    MAIL_PASSWORD: "BJDgD2022_"
  script:
    - echo "Deploying to production..."

    # Construction de l'image Docker
    - docker build -t $DOCKER_IMAGE .

    # Arrêt et suppression du conteneur existant
    - docker rm -f $DOCKER_CONTAINER_NAME || true

    # Copie du fichier .env
    - cp .env.example .env

    # Démarrage du nouveau conteneur avec les variables d'environnement
    - |
      docker run -d \
        --restart=always \
        -p $DOCKER_HOST_PORT:$SERVER_PORT \
        --name $DOCKER_CONTAINER_NAME \
        --env-file .env \
        $DOCKER_IMAGE

    # Vérification du déploiement
    - echo "Waiting for application to start..."
    - sleep 10
    - docker logs $DOCKER_CONTAINER_NAME --tail 20

    # Test de santé (optionnel)
    - echo "Testing application health..."
    - curl -f http://localhost:$DOCKER_HOST_PORT/actuator/health || echo "Health check failed"

  only:
    - main
  environment:
    name: production