stages:
  - build
  - deploy

variables:
  SERVER_PORT: "80"
  DOCKER_IMAGE: "erp-backend:latest"
  DOCKER_CONTAINER_NAME: "backend"
  DOCKER_HOST_PORT: "8082"

# Stage de build (optionnel, pour les tests)
build:
  stage: build
  image: maven:3.9.6-eclipse-temurin-17
  cache:
    paths:
      - .m2/repository
  variables:
    MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"
  script:
    - echo "Building application..."
    - mvn clean compile -DskipTests


# Déploiement en production
deploy_prod:
  stage: deploy
  tags:
    - docker-socket
  variables:
    # SPRING_PROFILES_ACTIVE: "prod"
    DB_DGD_PASSWORD: "rootoor"
    MAIL_PASSWORD: "BJDgD2022_"
  script:
    - echo "Deploying to production..."

    # Construction de l'image Docker
    - docker build -t $DOCKER_IMAGE .

    # Arrêt et suppression du conteneur existant
    - docker rm -f $DOCKER_CONTAINER_NAME || true

    # Démarrage du nouveau conteneur avec les variables d'environnement
    # - |
    #   docker run -d \
    #     --restart=always \
    #     -p $DOCKER_HOST_PORT:$SERVER_PORT \
    #     --name $DOCKER_CONTAINER_NAME \
    #     -e SERVER_PORT=$SERVER_PORT \
    #     -e SPRING_PROFILES_ACTIVE=$SPRING_PROFILES_ACTIVE \
    #     -e SECURITY_WHITE_LIST="$SECURITY_WHITE_LIST" \
    #     -e JWT_SECRET_KEY=$JWT_SECRET_KEY \
    #     -e JWT_DELAY=$JWT_DELAY \
    #     -e DB_DGD_USERNAME=$DB_DGD_USERNAME \
    #     -e DB_DGD_PASSWORD=$DB_DGD_PASSWORD \
    #     -e DB_DGD_URL=$DB_DGD_URL \
    #     -e DB_HOME_USERNAME=$DB_HOME_USERNAME \
    #     -e DB_HOME_PASSWORD=$DB_HOME_PASSWORD \
    #     -e DB_HOME_URL=$DB_HOME_URL \
    #     -e DB_TEST_USERNAME=$DB_TEST_USERNAME \
    #     -e DB_TEST_PASSWORD=$DB_TEST_PASSWORD \
    #     -e DB_TEST_URL=$DB_TEST_URL \
    #     -e MAIL_HOST=$MAIL_HOST \
    #     -e MAIL_USERNAME=$MAIL_USERNAME \
    #     -e MAIL_PASSWORD=$MAIL_PASSWORD \
    #     $DOCKER_IMAGE
    - |
      docker run -d \
        --restart=always \
        -p $DOCKER_HOST_PORT:$SERVER_PORT \
        --name $DOCKER_CONTAINER_NAME \
        -e SERVER_PORT=$SERVER_PORT \
        -e SPRING_PROFILES_ACTIVE=$SPRING_PROFILES_ACTIVE \
        -e DB_DGD_PASSWORD=$DB_DGD_PASSWORD \
        -e MAIL_PASSWORD=$MAIL_PASSWORD \
        $DOCKER_IMAGE

    # Vérification du déploiement
    - echo "Waiting for application to start..."
    - sleep 10
    - docker logs $DOCKER_CONTAINER_NAME --tail 20

    # Test de santé (optionnel)
    - echo "Testing application health..."
    - curl -f http://localhost:$DOCKER_HOST_PORT/actuator/health || echo "Health check failed"

  only:
    - main
  environment:
    name: production