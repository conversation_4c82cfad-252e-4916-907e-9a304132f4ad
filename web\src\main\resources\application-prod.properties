server.port=80

# Active le mode debug pour tous les logs # logging.level.root=debug # logging.level.org.springframework=debug
# logging.level.bj.douanes=debug

app.jwt.secret-key=${JWT_SECRET_KEY:9CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED8E}
app.jwt.delay=${JWT_DELAY:24}

app.security.white-list=${SECURITY_WHITE_LIST:/,/assets/**,/v3/api-docs/**,/swagger-ui/**,/swagger,/doc,/docs,/api/auth/**}


# ===================================== Datasource Configuration =====================================
app.datasources.typeNames=${DB_TYPES:dgd}

app.datasource.dgd.url=${DB_DGD_URL:***************************************}
app.datasource.dgd.username=${DB_DGD_USERNAME:root}
app.datasource.dgd.password=${DB_DGD_PASSWORD:}
# app.datasource.dgd.packages=bj.douanes.personal,bj.douanes.transport,bj.douanes.core

# ===================================== END Datasource Configuration =====================================


# ===================================== Mail Configuration =====================================
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.debug=true
spring.mail.port=587
spring.mail.host=${MAIL_HOST:mail.finances.bj}
spring.mail.username=${MAIL_USERNAME:<EMAIL>}
spring.mail.password=${MAIL_PASSWORD:}

# ===================================== END Mail Configuration =====================================