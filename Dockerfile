# Étape 1 : Build de l'application
FROM maven:3.9.6-eclipse-temurin-17 AS build
WORKDIR /app
# COPY pom.xml .
COPY . .
RUN mvn clean package -DskipTests

# Étape 2 : Image de production
FROM eclipse-temurin:17-jre
WORKDIR /app
COPY --from=build /app/web/target/*.jar app.jar

# Configuration du port d'exposition (par défaut 80)
ENV SERVER_PORT=80

# Configuration du profil Spring (par défaut prod)
ENV SPRING_PROFILES_ACTIVE=prod

# Configuration sécurité
ENV SECURITY_WHITE_LIST=/**

# Configuration JWT
ENV JWT_SECRET_KEY=9CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED122C8898D
ENV JWT_DELAY=24

# Configuration des bases de données
ENV DB_DGD_USERNAME=root
ENV DB_DGD_PASSWORD=

ENV DB_HOME_USERNAME=root
ENV DB_HOME_PASSWORD=

ENV DB_TEST_USERNAME=root
ENV DB_TEST_PASSWORD=

# # Configuration Mail
ENV MAIL_HOST=mail.finances.bj
ENV MAIL_USERNAME=<EMAIL>
ENV MAIL_PASSWORD=

EXPOSE $SERVER_PORT

ENTRYPOINT ["java", "-jar", "app.jar"]
