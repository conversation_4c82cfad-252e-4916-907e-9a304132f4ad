package bj.douanes.Services;

import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import bj.douanes.DTO.AgentCreationDto;
import bj.douanes.DTO.AgentCreationDto2;
import bj.douanes.DTO.AgentDto2;
import bj.douanes.DTO.BanqueInfoDTO;
import bj.douanes.DTO.BanqueInfoDTO2;
import bj.douanes.DTO.OrdreVirementDto;
import bj.douanes.Model.AgentTSD;
import bj.douanes.Model.Banque;
import bj.douanes.Model.Fonction;
import bj.douanes.Model.PrimeTsdAgent;
import bj.douanes.Model.RAgentBanque;
import bj.douanes.Model.RAgentBanqueId;
import bj.douanes.Model.RAgentFonction;
import bj.douanes.Model.RAgentFonctionId;
import bj.douanes.Model.RAgentRepartition;
import bj.douanes.Model.RAgentUnite;
import bj.douanes.Model.RAgentUniteId;
import bj.douanes.Model.Repartition;
import bj.douanes.Model.Unite;
import bj.douanes.Repository.AgentRepository;
import bj.douanes.Repository.BanqueRepository;
import bj.douanes.Repository.FonctionRepository;
import bj.douanes.Repository.PrimeTsdAgentRepository;
import bj.douanes.Repository.RAgentBanqueRepository;
import bj.douanes.Repository.RAgentFonctionRepository;
import bj.douanes.Repository.RAgentRepartitionRepository;
import bj.douanes.Repository.RAgentUniteRepository;
import bj.douanes.Repository.RepartitionRepo;
import bj.douanes.Repository.UniteRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class AgentCreationService {
    
    private final AgentRepository agentRepository;
    private final RAgentUniteRepository rAgentUniteRepository;
    private final RAgentFonctionRepository rAgentFonctionRepository;
    private final RAgentBanqueRepository rAgentBanqueRepository;
    private final BanqueRepository banqueRepository;
    private final UniteRepository uniteRepository;
    private final FonctionRepository fonctionRepository;
    private final RAgentRepartitionRepository rAgentRepartitionRepository;
    private final PrimeTsdAgentRepository primeTsdAgentRepository;
    private final RepartitionRepo repartitionRepository;


    public List<AgentDto2> getListAllAgentsRepart(Long idRepartition) {
        List<AgentDto2> result = new ArrayList<>();
        List<RAgentRepartition> repartitions = rAgentRepartitionRepository.findAllByIdRepartition(idRepartition);

        for (RAgentRepartition repartition : repartitions) {
            AgentTSD agent = repartition.getAgent();
            AgentDto2 dto = new AgentDto2();
            dto.setMatricule(agent.getMatricule());
            dto.setNom(agent.getNom());
            dto.setPrenoms(agent.getPrenoms());

            // Récupération des rattachements
            String agentUnite = rAgentUniteRepository.findCodeUniteByMatricule(agent.getMatricule());
            if (agentUnite != null) {
                dto.setCodeUnite(agentUnite);
            }
            String agentFonction = rAgentFonctionRepository.findCodeFonctionByMatricule(agent.getMatricule());
            if (agentFonction != null) {
                dto.setCodeFonction(agentFonction);
            }
            List<BanqueInfoDTO> agentBanque = rAgentBanqueRepository.findBanqueInfosByMatricule(agent.getMatricule());

            if (agentBanque != null && !agentBanque.isEmpty()) {
                dto.setCodeBanque(agentBanque.get(0).codeBanque());
                dto.setRib(agentBanque.get(0).rib());
            }

            // Récupération des informations de la prime
            PrimeTsdAgent prime = primeTsdAgentRepository.findByrAgentRepartition_Repartition_IdRepartition(idRepartition).stream()
                    .filter(p -> p.getRAgentRepartition().getAgent().getMatricule().equals(agent.getMatricule()))
                    .findFirst()
                    .orElse(null);
            if (prime != null) {
                dto.setMontantBonification(prime.getMontantBonification());
                dto.setPartGlobalReparti(prime.getPartGlobalReparti());
                dto.setTotalBrut(prime.getTotalBrut());
                dto.setTotalArrondi(prime.getTotalArrondi());
                dto.setMontantIts(prime.getMontantIts());
                dto.setItsArrondi(prime.getItsArrondi());
                dto.setMontantNetPayer(prime.getMontantNetPayer());
                dto.setMontantNetPayerAr(prime.getMontantNetPayerAr());
            }
            result.add(dto);
        }
        return result;
    }

    //Methode pour renvoyer la liste des agents et leurs rattachements
    public List<AgentCreationDto2> getListAgents() {
        List<AgentTSD> agents = agentRepository.findAll();

        // 1. Récupère tous les rattachements en une requête par type
        List<RAgentUnite> allUnites = rAgentUniteRepository.findAll();
        List<RAgentFonction> allFonctions = rAgentFonctionRepository.findAll();
        List<RAgentBanque> allBanques = rAgentBanqueRepository.findAll();

        // 2. Grouper par matricule pour accès rapide
        Map<String, List<RAgentUnite>> uniteMap = allUnites.stream()
            .collect(Collectors.groupingBy(u -> u.getId().getMatricule()));
        Map<String, List<RAgentFonction>> fonctionMap = allFonctions.stream()
            .collect(Collectors.groupingBy(f -> f.getId().getMatricule()));
        Map<String, List<RAgentBanque>> banqueMap = allBanques.stream()
            .collect(Collectors.groupingBy(b -> b.getId().getMatricule()));

        // 3. Parcours des agents
        return agents.stream().map(agent -> {
            AgentCreationDto2 dto = new AgentCreationDto2();
            dto.setMatricule(agent.getMatricule());
            dto.setNom(agent.getNom());
            dto.setPrenoms(agent.getPrenoms());
            dto.setSexe(agent.getSexe());
            dto.setDateNais(agent.getDateNais());
            dto.setIfu(agent.getIfu());
            dto.setTelephone(agent.getTelephone());
            dto.setEmail(agent.getEmail());
            dto.setActif(agent.getActif());

            // ========== UNITES ==========
            List<RAgentUnite> agentUnites = uniteMap.getOrDefault(agent.getMatricule(), Collections.emptyList());
            List<AgentCreationDto2.AgentUniteDto> uniteDtos = agentUnites.stream().map(u -> {
                AgentCreationDto2.AgentUniteDto uniteDto = new AgentCreationDto2.AgentUniteDto();
                uniteDto.setCodeUnite(u.getId().getCodeUnite());
                uniteDto.setDateDebut(u.getId().getDateDebut());
                uniteDto.setDateFin(u.getDateFin());
                return uniteDto;
            }).collect(Collectors.toList());
            dto.setUnites(uniteDtos);

            // ========== FONCTIONS ==========
            List<RAgentFonction> agentFonctions = fonctionMap.getOrDefault(agent.getMatricule(), Collections.emptyList());
            List<AgentCreationDto2.AgentFonctionDto> fonctionDtos = agentFonctions.stream().map(f -> {
                AgentCreationDto2.AgentFonctionDto fonctionDto = new AgentCreationDto2.AgentFonctionDto();
                fonctionDto.setCodeFonction(f.getId().getCodeFonction());
                fonctionDto.setDateDebut(f.getId().getDateDebut());
                fonctionDto.setDateFin(f.getDateFin());
                return fonctionDto;
            }).collect(Collectors.toList());
            dto.setFonctions(fonctionDtos);

            // ========== BANQUES ==========
            List<RAgentBanque> agentBanques = banqueMap.getOrDefault(agent.getMatricule(), Collections.emptyList());
            List<AgentCreationDto2.AgentBanqueDto> banqueDtos = agentBanques.stream().map(b -> {
                AgentCreationDto2.AgentBanqueDto banqueDto = new AgentCreationDto2.AgentBanqueDto();
                banqueDto.setCodeBanque(b.getId().getCodeBanque());
                banqueDto.setRib(b.getRib());
                banqueDto.setDateDebut(b.getId().getDateDebut());
                banqueDto.setDateFin(b.getDateFin());
                Banque banque = b.getBanque();
                if (banque != null) {
                    AgentCreationDto2.BanqueDto banqueInfo = new AgentCreationDto2.BanqueDto();
                    banqueInfo.setCodeBanque(banque.getCodeBanque());
                    banqueInfo.setNomBanque(banque.getNomBanque());
                    banqueDto.setBanque(banqueInfo);
                }
                return banqueDto;
            }).collect(Collectors.toList());
            dto.setBanques(banqueDtos);

            return dto;
        }).collect(Collectors.toList());
    }

    // Création de l'agent
    @Transactional
    public void createAgentFromDto(AgentCreationDto dto) {
    
        AgentTSD agent = new AgentTSD();
        agent.setMatricule(dto.getMatricule());
        agent.setNom(dto.getNom());
        agent.setPrenoms(dto.getPrenoms());
        agent.setSexe(dto.getSexe());
        agent.setDateNais(dto.getDateNais());
        agent.setIfu(dto.getIfu());
        agent.setTelephone(dto.getTelephone());
        agent.setEmail(dto.getEmail());
        agent.setActif(dto.getActif());
        agentRepository.save(agent);

        // Rattachement unité
        AgentCreationDto.AgentUniteDto uniteDto = dto.getUnites();
        RAgentUniteId uniteId = new RAgentUniteId();
        uniteId.setMatricule(agent.getMatricule());
        uniteId.setCodeUnite(uniteDto.getCodeUnite());
        uniteId.setDateDebut(uniteDto.getDateDebut());

        Unite unite = uniteRepository.findById(uniteDto.getCodeUnite())
            .orElseThrow(() -> new RuntimeException("Unité non trouvée avec code : " + uniteDto.getCodeUnite()));

        RAgentUnite agentUnite = new RAgentUnite();
        agentUnite.setId(uniteId);
        agentUnite.setAgent(agent);
        agentUnite.setUnite(unite);
        agentUnite.setDateFin(uniteDto.getDateFin());
        rAgentUniteRepository.save(agentUnite);


        // Rattachement fonction
        AgentCreationDto.AgentFonctionDto fonctionDto = dto.getFonctions();
        RAgentFonctionId fonctionId = new RAgentFonctionId();
        fonctionId.setMatricule(agent.getMatricule());
        fonctionId.setCodeFonction(fonctionDto.getCodeFonction());
        fonctionId.setDateDebut(fonctionDto.getDateDebut());

        Fonction fonctionEntity = fonctionRepository.findById(fonctionDto.getCodeFonction())
            .orElseThrow(() -> new RuntimeException("Fonction non trouvée avec code : " + fonctionDto.getCodeFonction()));

        RAgentFonction fonction = new RAgentFonction();
        fonction.setId(fonctionId);
        fonction.setAgent(agent);
        fonction.setFonction(fonctionEntity); 
        fonction.setDateFin(fonctionDto.getDateFin());
        rAgentFonctionRepository.save(fonction);


        // Rattachement banque
        AgentCreationDto.AgentBanqueDto banqueDto = dto.getBanques();
        Banque banque = banqueRepository.findById(banqueDto.getCodeBanque()).orElseGet(() -> {
            Banque newBanque = new Banque();
            newBanque.setCodeBanque(banqueDto.getCodeBanque());
            newBanque.setNomBanque(banqueDto.getBanque().getNomBanque());
            return banqueRepository.save(newBanque);
        });

        RAgentBanqueId banqueId = new RAgentBanqueId();
        banqueId.setMatricule(agent.getMatricule());
        banqueId.setCodeBanque(banqueDto.getCodeBanque());
        banqueId.setDateDebut(banqueDto.getDateDebut());

        RAgentBanque raBanque = new RAgentBanque();
        raBanque.setId(banqueId);
        raBanque.setAgent(agent);
        raBanque.setBanque(banque);
        raBanque.setRib(banqueDto.getRib());
        raBanque.setDateFin(banqueDto.getDateFin());
        rAgentBanqueRepository.save(raBanque);
    }

    //Mettre a jour AgentFromDto
    @Transactional
    public void updateAgentFromDto(String matricule, AgentCreationDto dto) {
        // 1. Charger l'agent existant
        AgentTSD agent = agentRepository.findById(matricule)
            .orElseThrow(() -> new RuntimeException("Agent non trouvé avec matricule : " + matricule));

        // 2. Mettre à jour les champs simples
        agent.setNom(dto.getNom());
        agent.setPrenoms(dto.getPrenoms());
        agent.setSexe(dto.getSexe());
        agent.setDateNais(dto.getDateNais());
        agent.setIfu(dto.getIfu());
        agent.setTelephone(dto.getTelephone());
        agent.setEmail(dto.getEmail());
        agent.setActif(dto.getActif());
        agent.setUpdatedAt(LocalDate.now());
        agentRepository.save(agent);

        // 3. Mise à jour du rattachement unité
        AgentCreationDto.AgentUniteDto uniteDto = dto.getUnites();
        RAgentUniteId uniteId = new RAgentUniteId();
        uniteId.setMatricule(agent.getMatricule());
        uniteId.setCodeUnite(uniteDto.getCodeUnite());
        uniteId.setDateDebut(uniteDto.getDateDebut());

        Unite unite = uniteRepository.findById(uniteDto.getCodeUnite())
            .orElseThrow(() -> new RuntimeException("Unité non trouvée avec code : " + uniteDto.getCodeUnite()));

        RAgentUnite agentUnite = rAgentUniteRepository.findAgentById(uniteId);
        if (agentUnite == null) {
            agentUnite = new RAgentUnite();
            agentUnite.setId(uniteId);
            agentUnite.setAgent(agent);
            agentUnite.setUnite(unite);
            agentUnite.setCreatedAt(LocalDate.now());
        }
        agentUnite.setDateFin(uniteDto.getDateFin());
        agentUnite.setUpdatedAt(LocalDate.now());
        rAgentUniteRepository.save(agentUnite);

        // 4. Mise à jour du rattachement fonction
        AgentCreationDto.AgentFonctionDto fonctionDto = dto.getFonctions();
        RAgentFonctionId fonctionId = new RAgentFonctionId();
        fonctionId.setMatricule(agent.getMatricule());
        fonctionId.setCodeFonction(fonctionDto.getCodeFonction());
        fonctionId.setDateDebut(fonctionDto.getDateDebut());

        Fonction fonctionEntity = fonctionRepository.findById(fonctionDto.getCodeFonction())
            .orElseThrow(() -> new RuntimeException("Fonction non trouvée avec code : " + fonctionDto.getCodeFonction()));

        RAgentFonction fonction = rAgentFonctionRepository.findAgentById(fonctionId);
        if (fonction == null) {
            fonction = new RAgentFonction();
            fonction.setId(fonctionId);
            fonction.setAgent(agent);
            fonction.setFonction(fonctionEntity);
            fonction.setCreatedAt(LocalDate.now());
        }
        fonction.setDateFin(fonctionDto.getDateFin());
        fonction.setUpdatedAt(LocalDate.now());
        rAgentFonctionRepository.save(fonction);

        // 5. Mise à jour du rattachement banque
        AgentCreationDto.AgentBanqueDto banqueDto = dto.getBanques();
        Banque banque = banqueRepository.findById(banqueDto.getCodeBanque()).orElseGet(() -> {
            Banque newBanque = new Banque();
            newBanque.setCodeBanque(banqueDto.getCodeBanque());
            newBanque.setNomBanque(banqueDto.getBanque().getNomBanque());
            return banqueRepository.save(newBanque);
        });

        RAgentBanqueId banqueId = new RAgentBanqueId();
        banqueId.setMatricule(agent.getMatricule());
        banqueId.setCodeBanque(banqueDto.getCodeBanque());
        banqueId.setDateDebut(banqueDto.getDateDebut());

        RAgentBanque raBanque = rAgentBanqueRepository.findAgentById(banqueId);
        if (raBanque == null) {
            raBanque = new RAgentBanque();
            raBanque.setId(banqueId);
            raBanque.setAgent(agent);
            raBanque.setBanque(banque);
            raBanque.setCreatedAt(LocalDate.now());
        }
        raBanque.setRib(banqueDto.getRib());
        raBanque.setDateFin(banqueDto.getDateFin());
        raBanque.setUpdatedAt(LocalDate.now());
        rAgentBanqueRepository.save(raBanque);
    }


    //methode pour telecharger au format excel la liste des agents repartie a partir de idRepartition
    public byte[] downloadAgentsExcel(Long idRepartition) throws IOException {
       List<AgentDto2> result = new ArrayList<>();
       List<RAgentRepartition> repartitions = rAgentRepartitionRepository.findAllByIdRepartition(idRepartition);

        for (RAgentRepartition repartition : repartitions) {
            AgentTSD agent = repartition.getAgent();
            AgentDto2 dto = new AgentDto2();
            dto.setMatricule(agent.getMatricule());
            dto.setNom(agent.getNom());
            dto.setPrenoms(agent.getPrenoms());

           // Récupération des rattachements
              String agentUnite = rAgentUniteRepository.findCodeUniteByMatricule(agent.getMatricule());
           if (agentUnite != null) {
               dto.setCodeUnite(agentUnite);
           }
           String agentFonction = rAgentFonctionRepository.findCodeFonctionByMatricule(agent.getMatricule());
           if (agentFonction != null) {
               dto.setCodeFonction(agentFonction);
           }
           List<BanqueInfoDTO> agentBanque = rAgentBanqueRepository.findBanqueInfosByMatricule(agent.getMatricule());

           if (agentBanque != null && !agentBanque.isEmpty()) {
               dto.setCodeBanque(agentBanque.get(0).codeBanque());
               dto.setRib(agentBanque.get(0).rib());
           }
              // Récupération des informations de la prime
              PrimeTsdAgent prime = primeTsdAgentRepository.findByrAgentRepartition_Repartition_IdRepartition(idRepartition).stream()
                     .filter(p -> p.getRAgentRepartition().getAgent().getMatricule().equals(agent.getMatricule()))
                     .findFirst()
                     .orElse(null);
           if (prime != null) {
               dto.setMontantBonification(prime.getMontantBonification());
               dto.setPartGlobalReparti(prime.getPartGlobalReparti());
               dto.setTotalBrut(prime.getTotalBrut());
               dto.setTotalArrondi(prime.getTotalArrondi());
               dto.setMontantIts(prime.getMontantIts());
               dto.setItsArrondi(prime.getItsArrondi());
               dto.setMontantNetPayer(prime.getMontantNetPayer());
                dto.setMontantNetPayerAr(prime.getMontantNetPayerAr());
           }
           result.add(dto);
       }
        byte[] excelFile = ExcelGenerator.generateExcel(result);
        return excelFile;
    }

    // Methode pour telecharger la liste des ordres de virement au format excel
    public byte[] downloadOrdreVirementExcel(Long idRepartition) throws IOException {
       List<OrdreVirementDto> result = new ArrayList<>();
       List<RAgentRepartition> repartitions = rAgentRepartitionRepository.findAllByIdRepartition(idRepartition);

        for (RAgentRepartition repartition : repartitions) {
            AgentTSD agent = repartition.getAgent();
            OrdreVirementDto dto = new OrdreVirementDto();
            dto.setMatricule(agent.getMatricule());
            dto.setNom(agent.getNom());
            dto.setPrenoms(agent.getPrenoms());

           //Recuperation des infos de la repartition
              Repartition repartitionInfo = repartitionRepository.findByIdRepartition(idRepartition);
           if (repartitionInfo != null) {
               dto.setAnnee(repartitionInfo.getAnnee());
               dto.setTypePrime(repartitionInfo.getCodeTypePrime());
                dto.setPeriode(repartitionInfo.getPeriode());
           }

           List<BanqueInfoDTO2> agentBanque = rAgentBanqueRepository.findBanqueInfosByMatricule2(agent.getMatricule());

           if (agentBanque != null && !agentBanque.isEmpty()) {
               dto.setCodeBanque(agentBanque.get(0).codeBanque());
               dto.setRib(agentBanque.get(0).rib());
               dto.setNomBanque(agentBanque.get(0).nomBanque());
           }
              // Récupération des informations de la prime
              PrimeTsdAgent prime = primeTsdAgentRepository.findByrAgentRepartition_Repartition_IdRepartition(idRepartition).stream()
                     .filter(p -> p.getRAgentRepartition().getAgent().getMatricule().equals(agent.getMatricule()))
                     .findFirst()
                     .orElse(null);
           if (prime != null) {
               dto.setMontantNetPayer(prime.getMontantNetPayer());
               dto.setMontantNetPayerAr(prime.getMontantNetPayerAr());
           }

           
           result.add(dto);
       }
            //Trier l'ordre de virement par codeBanque
            result.sort((o1, o2) -> {
                if (o1.getCodeBanque() == null && o2.getCodeBanque() == null) {
                    return 0;
                }
                if (o1.getCodeBanque() == null) {
                    return 1;
                }
                if (o2.getCodeBanque() == null) {
                    return -1;
                }
                return o1.getCodeBanque().compareTo(o2.getCodeBanque());
            });
        byte[] excelFile = OrdreVirementGenerator.generateOrdreVirementExcel(result);
        return excelFile;
    }


    //recuperer tous les agents et leurs informations montants repartition, banque, fonction et unite
    // public List<AgentDto> getAllAgentDoRepartPrime(Long idRepartition) {
    //     List<AgentDto> result = new ArrayList<>();
    //     // Récupération de tous les agents de la répartition
    //     List<RAgentRepartition> repartitions = rAgentRepartitionRepository.findAllByIdRepartition(idRepartition);

    //     for (RAgentRepartition repartition : repartitions) {
    //         AgentTSD agent = repartition.getAgent();
    //         AgentDto agentDto = new AgentDto();

    //         // Remplissage des informations principales de l'agent
    //         agentDto.setMatricule(agent.getMatricule());
    //         agentDto.setNom(agent.getNom());
    //         agentDto.setPrenoms(agent.getPrenoms());

    //         // Informations d'unité (on prend le plus récent, ou adapte selon ta logique)
    //         List<RAgentUnite> agentUnites = rAgentUniteRepository.findAllByMatricule(agent.getMatricule());
    //         if (!agentUnites.isEmpty()) {
    //             RAgentUnite agentUnite = agentUnites.get(0); // ou choisis la bonne
    //             AgentDto.AgentUniteDto uniteDto = new AgentDto.AgentUniteDto();
    //             uniteDto.setCodeUnite(agentUnite.getId().getCodeUnite());
    //             agentDto.setUnites(uniteDto);
    //         }

    //         // Informations de fonction
    //         List<RAgentFonction> agentFonctions = rAgentFonctionRepository.findAllByMatricule(agent.getMatricule());
    //         if (!agentFonctions.isEmpty()) {
    //             RAgentFonction agentFonction = agentFonctions.get(0);
    //             AgentDto.AgentFonctionDto fonctionDto = new AgentDto.AgentFonctionDto();
    //             fonctionDto.setCodeFonction(agentFonction.getId().getCodeFonction());
    //             agentDto.setFonctions(fonctionDto);
    //         }

    //         // Informations bancaires
    //         List<RAgentBanque> agentBanques = rAgentBanqueRepository.findAllByMatricule(agent.getMatricule());
    //         if (!agentBanques.isEmpty()) {
    //             RAgentBanque agentBanque = agentBanques.get(0);
    //             AgentDto.AgentBanqueDto banqueDto = new AgentDto.AgentBanqueDto();
    //             banqueDto.setCodeBanque(agentBanque.getId().getCodeBanque());
    //             banqueDto.setRib(agentBanque.getRib());
                
    //             Banque banque = agentBanque.getBanque();
    //             if (banque != null) {
    //                 AgentDto.BanqueDto banqueInfo = new AgentDto.BanqueDto();
    //                 banqueInfo.setCodeBanque(banque.getCodeBanque());
    //                 banqueInfo.setNomBanque(banque.getNomBanque());
    //                 banqueDto.setBanque(banqueInfo);
    //             }
    //             agentDto.setBanques(banqueDto);
    //         }

    //         // Récupération et mapping de la prime
    //         // Normalement, il n'y a qu'une prime par répartition pour un agent
    //         PrimeTsdAgent prime = primeTsdAgentRepository.findByrAgentRepartition_Repartition_IdRepartition(idRepartition).stream()
    //                  .filter(p -> p.getRAgentRepartition().getAgent().getMatricule().equals(agent.getMatricule()))
    //                  .findFirst()
    //                  .orElse(null);

    //         if (prime != null) {
    //             AgentDto.PrimeTsdAgentDto primeDto = new AgentDto.PrimeTsdAgentDto();
    //             primeDto.setMontantBonification(prime.getMontantBonification());
    //             primeDto.setPartGlobalReparti(prime.getPartGlobalReparti());
    //             primeDto.setTotalBrut(prime.getTotalBrut());
    //             primeDto.setTotalArrondi(prime.getTotalArrondi());
    //             primeDto.setMontantIts(prime.getMontantIts());
    //             primeDto.setItsArrondi(prime.getItsArrondi());
    //             primeDto.setMontantNetPayer(prime.getMontantNetPayer());
    //             agentDto.setPrimeTsdAgent(primeDto);
    //         }

    //         // Ajout de l'agent à la liste finale
    //         result.add(agentDto);
    //     }
    //     return result;
    // }
}

