package bj.douanes.web.Primes;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.core.shared.response.AppResponse;
import bj.douanes.facade.Prime.TypePrimeFacade;
import bj.douanes.facade.UTILS.DTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;


@RestController
@RequiredArgsConstructor
@RequestMapping("api/prime/typeprime")
@Tag(name = "TypePrime", description = "API pour creer des primes et les modifier")
public class TypePrimeController {

    private final TypePrimeFacade typePrimeFacade;

    @GetMapping("/primes")
    @Operation(summary = "Get all primes", description = "API pour afficher la liste de toutes les primes")
    public ResponseEntity<?> getAllPrimes() {
        return AppResponse.ok(typePrimeFacade.getAllPrimes());
    }

    @GetMapping("/primes/{id}")
    @Operation(summary = "Get prime by ID", description = "API pour afficher une prime par son ID")
    public ResponseEntity<?> getPrimeById(@PathVariable String id) {
        return AppResponse.ok(typePrimeFacade.getPrimeById(id));
    }
    @PostMapping("/createTypePrime")
    @Operation(summary = "Create a new prime", description = "API pour creer une nouvelle prime")
    public ResponseEntity<?> createPrime(@RequestBody DTO.TypePrimeDto primeDto) {
        return AppResponse.created(typePrimeFacade.createPrime(primeDto));
    }
    @PutMapping("/update/{id}")
    @Operation(summary = "Update an existing prime", description = "API pour mettre a jour les informations d'une prime existante")
    public ResponseEntity<?> updatePrime(@RequestBody DTO.TypePrimeDto primeDto, @PathVariable String id) {
        return AppResponse.ok(typePrimeFacade.updatePrime(id, primeDto));
    }
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "Delete a prime", description = "API pour supprimer une prime")
    public ResponseEntity<?> deletePrime(@PathVariable String id) {
        typePrimeFacade.deletePrime(id);
        return AppResponse.ok("TypePrime deleted successfully");
    }
   
}
