package bj.douanes.Services;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;

import bj.douanes.DTO.MontantCollectParUniteDto;
import bj.douanes.DTO.PrimeTSDUniteDto;
import bj.douanes.DTO.PrimeTsdAgentDto;
import bj.douanes.Model.AgentTSD;
import bj.douanes.Model.PrimeTSD;
import bj.douanes.Model.PrimeTSDUnite;
import bj.douanes.Model.PrimeTsdAgent;
import bj.douanes.Model.RAgentRepartition;
import bj.douanes.Model.RAgentRepartitionId;
import bj.douanes.Model.RUniteRepartition;
import bj.douanes.Model.RUniteRepartitionId;
import bj.douanes.Model.Repartition;
import bj.douanes.Model.Unite;
import bj.douanes.Repository.AgentRepository;
import bj.douanes.Repository.PrimeTSDRepo;
import bj.douanes.Repository.PrimeTsdAgentRepository;
import bj.douanes.Repository.PrimeTsdUniteRepository;
import bj.douanes.Repository.RAgentRepartitionRepository;
import bj.douanes.Repository.RAgentUniteRepository;
import bj.douanes.Repository.RFonctionTypePrimeRepository;
import bj.douanes.Repository.RUniteRepartitionRepository;
import bj.douanes.Repository.RepartitionRepo;
import bj.douanes.Repository.UniteRepository;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

public interface RepartitionServ {
    Repartition createRepartition(Repartition repartition, List<MontantCollectParUniteDto> dtoList);
    Repartition getRepartitionById(Long id);
    List<Repartition> getAllRepartitions();
    Repartition updateRepartition(Long id, Repartition repartition);
    void deleteRepartition(Long id);
}

@RequiredArgsConstructor
@Service
class InnerRepartitionServ implements RepartitionServ {
    
    private final RepartitionRepo repartitionRepo;
    private final AgentRepository agentRepository;
    private final RAgentRepartitionRepository rAgentRepartitionRepository;
    private final PrimeTsdAgentRepository primeTsdAgentRepository;
    private final PrimeTSDRepo primeTSDRepository;
    private final RUniteRepartitionRepository rUniteRepartitionRepository;
    private final PrimeTsdUniteRepository primeTsdUniteRepository;
    private final RUniteRepartitionRepository rUniteRepo;
    private final PrimeTsdUniteRepository primeUniteRepo;
    private final UniteRepository uniteRepo;
    private final RFonctionTypePrimeRepository rFonctionTypePrimeRepository;
    private final PrimeTSDRepo primeTSDRepo;
    private final RAgentUniteRepository rAgentUniteRepository;


    @Transactional
    @Override
    public Repartition createRepartition(Repartition repartition, List<MontantCollectParUniteDto> dtoList) {
        Repartition saved = repartitionRepo.save(repartition);

        List<AgentTSD> agents = agentRepository.findAllByActifTrue();
        List<RAgentRepartition> links = new ArrayList<>();

        for (AgentTSD agent : agents) {
            RAgentRepartitionId id = new RAgentRepartitionId();
            id.setMatricule(agent.getMatricule());
            id.setIdRepartition(saved.getIdRepartition());

            RAgentRepartition link = new RAgentRepartition();
            link.setId(id);
            link.setRepartition(saved);
            link.setAgent(agent);
            link.setCreatedAt(LocalDate.now());
            link.setUpdatedAt(LocalDate.now());

            links.add(link);
        }

        //insertion en une seule opération
        rAgentRepartitionRepository.saveAll(links);
        //reuperer le numero de repartition
        Long idRepartition = saved.getIdRepartition();

        createVersementUnites(dtoList, idRepartition);
        createPrimeTsdAgent(idRepartition);

        return saved;
    }


    @Override
    public Repartition getRepartitionById(Long id) {
        return repartitionRepo.findById(id).orElse(null);
    }

    @Override
    public List<Repartition> getAllRepartitions() {
        return repartitionRepo.findAll();
    }

    @Override
    public Repartition updateRepartition(Long id, Repartition repartition) {
        Repartition existingRepartition = repartitionRepo.findById(id).orElse(null);
        if (existingRepartition == null) {
            return null;
        }
        existingRepartition.setCodeTypePrime(repartition.getCodeTypePrime());
        //existingRepartition.setIdPrime(repartition.getIdPrime());
        existingRepartition.setPeriode(repartition.getPeriode());
        existingRepartition.setAnnee(repartition.getAnnee());
        return repartitionRepo.save(existingRepartition);
    }

    @Transactional
    @Override
    public void deleteRepartition(Long id) {
        primeTsdAgentRepository.deleteByRepartitionId(id);
        primeTSDRepository.deleteByRepartitionId(id);
        rAgentRepartitionRepository.deleteByRepartitionId(id);
        primeTsdUniteRepository.deleteByRepartitionId(id);
        rUniteRepartitionRepository.deleteByRepartitionId(id);
        repartitionRepo.deleteById(id);
    }


    @Transactional
    public List<PrimeTSDUnite> createVersementUnites(List<MontantCollectParUniteDto> dtoList, Long idRepartition) {
        Repartition repartition = repartitionRepo.findById(idRepartition)
            .orElseThrow(() -> new EntityNotFoundException("Répartition introuvable"));

        List<PrimeTSDUnite> results = new ArrayList<>();

        for (MontantCollectParUniteDto dto : dtoList) {
            Unite unite = uniteRepo.findById(dto.getCodeUnite())
                .orElseThrow(() -> new EntityNotFoundException("Unité " + dto.getCodeUnite() + " introuvable"));

            RUniteRepartition uniteRepartition = new RUniteRepartition();
            RUniteRepartitionId compositeId = new RUniteRepartitionId(dto.getCodeUnite(), idRepartition);
            uniteRepartition.setId(compositeId);
            uniteRepartition.setUnite(unite);
            uniteRepartition.setRepartition(repartition);
            uniteRepartition.setCreatedAt(LocalDate.now());
            uniteRepartition.setUpdatedAt(LocalDate.now());
            rUniteRepo.save(uniteRepartition);

            PrimeTSDUnite primeUnite = new PrimeTSDUnite();
            primeUnite.setRUniteRepartition(uniteRepartition);
            primeUnite.setMontantVerset(dto.getMontantVerset());
            primeUnite.setOeuvreSocialUnite(dto.getMontantVerset().multiply(BigDecimal.valueOf(0.1)));

            BigDecimal reste = dto.getMontantVerset().subtract(primeUnite.getOeuvreSocialUnite());

            primeUnite.setBonificationUnite(reste.multiply(BigDecimal.valueOf(0.2)));
            primeUnite.setPartUnite(reste.multiply(BigDecimal.valueOf(0.8)));
            BigDecimal cumulCoef = rFonctionTypePrimeRepository.getCumulCoefParUniteEtTypePrime(dto.getCodeUnite());
            primeUnite.setCumulCoef(cumulCoef);

            PrimeTSDUnite savedPrimeUnite = primeUniteRepo.save(primeUnite);

            uniteRepartition.setIdPrimeUnite(savedPrimeUnite.getIdPrimeUnite());
            rUniteRepo.save(uniteRepartition);

            results.add(savedPrimeUnite);
        }

        // Calcul des totaux globaux pour charger PrimeTSD 
        BigDecimal montantOeuvreSocialGlobal = BigDecimal.ZERO;
        BigDecimal partGlobal = BigDecimal.ZERO;
        for (PrimeTSDUnite item : results) {
            montantOeuvreSocialGlobal = montantOeuvreSocialGlobal.add(item.getOeuvreSocialUnite());
            partGlobal = partGlobal.add(item.getPartUnite());
        }
        BigDecimal cumulCoefAllAgents = rFonctionTypePrimeRepository.getCumulCoefTSDDesAgentsActifs();

        PrimeTSD primeTSD = new PrimeTSD();
        primeTSD.setOeuvreSocialGlobal(montantOeuvreSocialGlobal);
        primeTSD.setPartGlobal(partGlobal);
        primeTSD.setCumulCoefGlobal(cumulCoefAllAgents);
        primeTSD.setRepartition(repartition);
        primeTSDRepo.save(primeTSD);

        System.out.println("Montant Oeuvre Sociale Global: " + primeTSD.getOeuvreSocialGlobal());
        System.out.println("Part Global: " + primeTSD.getPartGlobal());
        System.out.println("Cumul Coefficient Global: " + primeTSD.getCumulCoefGlobal());

        return results;
    }

    @Transactional
    public List<PrimeTsdAgentDto> createPrimeTsdAgent(Long idRepartition) {

        List<RAgentRepartition> rAgentRepartitions = rAgentRepartitionRepository.findAllByIdRepartition(idRepartition);
        List<PrimeTsdAgent> createdAgents = new ArrayList<>(); // Liste pour stocker les nouveaux agents

        for (RAgentRepartition rAgentRepartition : rAgentRepartitions) {
            //recuperer le matricule
            RAgentRepartitionId id = rAgentRepartition.getId();
            String matricule = id.getMatricule();
            String codeUnite = rAgentUniteRepository.findCodeUniteByMatricule(matricule);
            System.out.println("Code Unite Agent: " + codeUnite + ", Matricule: " + matricule);
            
            PrimeTsdAgent primeTsdAgent = new PrimeTsdAgent();

            // Récupérer les informations de PrimeTSDUnite
            PrimeTSDUniteDto primeTSDUniteDto = primeTsdUniteRepository.findByIdRepartitionAndCodeUnite(idRepartition, codeUnite);
            System.out.println("PrimeTSDUniteDto: " + primeTSDUniteDto);

            BigDecimal montantBonif = primeTSDUniteDto != null ? primeTSDUniteDto.getBonificationUnite(): BigDecimal.ZERO;

            BigDecimal cumulCoef = rFonctionTypePrimeRepository.getCumulCoefParUniteTSDDesAgentsActifs(codeUnite);
            System.out.println("Cumul Coef: " + cumulCoef);

            BigDecimal coefAgent = rFonctionTypePrimeRepository.getCoefficientByMatriculeAndTypePrime(matricule);
            System.out.println("Coefficient Agent: " + coefAgent);

            // Calcul du montant de bonification
            BigDecimal montantBonification = BigDecimal.ZERO;
            if (cumulCoef.compareTo(BigDecimal.ZERO) > 0) {
                montantBonification = coefAgent.multiply(montantBonif).divide(cumulCoef, 2, RoundingMode.HALF_UP);
            }

            PrimeTSD primeTSD = primeTSDRepository.findByRepartitionIdRepartition(idRepartition);
            if (primeTSD == null) {
                continue;
            }

            BigDecimal montantGlobal = primeTSD.getPartGlobal();
            System.out.println("Montant Global: " + montantGlobal);
            BigDecimal cumulCoefficient = primeTSD.getCumulCoefGlobal();
            System.out.println("Cumul Coefficient Global: " + cumulCoefficient);

            BigDecimal partGlobalAgent = BigDecimal.ZERO;
            if (cumulCoefficient.compareTo(BigDecimal.ZERO) > 0) {//verifier si le denominateur est > a 0
                partGlobalAgent = montantGlobal.multiply(coefAgent).divide(cumulCoefficient, 2, RoundingMode.HALF_UP);
                System.out.println("Part Global Agent: " + partGlobalAgent);
            }

            BigDecimal totalBrut = partGlobalAgent.add(montantBonification);
            System.out.println("Total Brut: " + totalBrut);
            //afficher le matricule,codeUnite, montantBonification, partGlobalAgent, totalBrut
            System.out.println("====================>Matricule: " + matricule + ", Code Unite: " + codeUnite +
                    ", Montant Bonification: " + montantBonification +
                    ", Part Global Agent: " + partGlobalAgent +
                    ", Total Brut: " + totalBrut+" <====================");

            // Remplir les champs de PrimeTsdAgent
            primeTsdAgent.setMontantBonification(montantBonification);
            primeTsdAgent.setPartGlobalReparti(partGlobalAgent);
            primeTsdAgent.setTotalBrut(totalBrut);

            //CALCUL DE TOTAL ARRONDI EN FAISANT (TOTAL BRUT/1000)*1000
            BigDecimal totalArrondi = totalBrut.divide(BigDecimal.valueOf(1000), 0, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(1000));
            System.out.println("Total Arrondi: " + totalArrondi);
            primeTsdAgent.setTotalArrondi(totalArrondi);

            //CALCUL DU MONTANT ITS SELON =SI(totalArrondi<=60000;0;SI(totalArrondi<=150000;(totalArrondi-60000)*10%;SI(totalArrondi<=250000;9000+(totalArrondi-150000)*15%;SI(totalArrondi<=500000;24000+(totalArrondi-250000)*19%;71500+(totalArrondi-500000)*30%))))
            BigDecimal montantIts = BigDecimal.ZERO;
            if (totalArrondi.compareTo(BigDecimal.valueOf(60000)) <= 0) {
                montantIts = BigDecimal.ZERO;
            } else if (totalArrondi.compareTo(BigDecimal.valueOf(150000)) <= 0) {
                montantIts = totalArrondi.subtract(BigDecimal.valueOf(60000)).multiply(BigDecimal.valueOf(0.10));
            } else if (totalArrondi.compareTo(BigDecimal.valueOf(250000)) <= 0) {
                montantIts = BigDecimal.valueOf(9000).add(totalArrondi.subtract(BigDecimal.valueOf(150000)).multiply(BigDecimal.valueOf(0.15)));
            } else if (totalArrondi.compareTo(BigDecimal.valueOf(500000)) <= 0) {
                montantIts = BigDecimal.valueOf(24000).add(totalArrondi.subtract(BigDecimal.valueOf(250000)).multiply(BigDecimal.valueOf(0.19)));
            } else {
                montantIts = BigDecimal.valueOf(71500).add(totalArrondi.subtract(BigDecimal.valueOf(500000)).multiply(BigDecimal.valueOf(0.30)));
            }
            System.out.println("Montant ITS: " + montantIts);
            primeTsdAgent.setMontantIts(montantIts);
            
            //CALCUL DE L'ITS ARRONDI EN FAISANT ARRONDI(montantIts;-1)
            BigDecimal itsArrondi = montantIts.setScale(-1, RoundingMode.HALF_UP);
            System.out.println("ITS Arrondi: " + itsArrondi);
            primeTsdAgent.setItsArrondi(itsArrondi);

            //CALCUL DU MONTANT NET A PAYER EN FAISANT totalBrut - itsArrondi
            BigDecimal montantNetPayer = totalBrut.subtract(itsArrondi);
            
            System.out.println("Montant Net à Payer: " + montantNetPayer);
            primeTsdAgent.setMontantNetPayer(montantNetPayer);

            //supprimer la partie décimale de montantNetPayer
            BigDecimal montantNetPayerAr = BigDecimal.valueOf(montantNetPayer.longValue()).setScale(0, RoundingMode.DOWN); // Arrondir vers le bas
            System.out.println("Montant Net à Payer Arrondi: " + montantNetPayerAr);
            primeTsdAgent.setMontantNetPayerAr(montantNetPayerAr);
            // Afficher les informations de l'agent 
            System.out.println("--------------->Agent repartie: " + matricule + ", Montant Bonification: " + montantBonification +
                    ", Part Global Reparti: " + partGlobalAgent +
                    ", Total Brut: " + totalBrut +
                    ", Total Arrondi: " + totalArrondi +
                    ", Montant ITS: " + montantIts +
                    ", ITS Arrondi: " + itsArrondi +
                    ", Montant Net à Payer: " + montantNetPayer +
                    ", Montant Net à Payer Arrondi: " + montantNetPayerAr + " <---------------");

            // Charger RAgentRepartition existant depuis la base
            RAgentRepartitionId rAgentRepartitionId = new RAgentRepartitionId();
            rAgentRepartitionId.setMatricule(matricule);
            rAgentRepartitionId.setIdRepartition(idRepartition);

            Optional<RAgentRepartition> rAgentRepartitionOpt = rAgentRepartitionRepository.findById(rAgentRepartitionId);

            if (!rAgentRepartitionOpt.isPresent()) {
                continue;
            }

            primeTsdAgent.setRAgentRepartition(rAgentRepartitionOpt.get());

            PrimeTsdAgent saved = primeTsdAgentRepository.save(primeTsdAgent); // Sauvegarde
            rAgentRepartitionOpt.get().setIdPrimeAgent(saved.getIdPrimeAgent()); // Mettre à jour l'ID de l'agent dans RAgentRepartition
            rAgentRepartitionRepository.save(rAgentRepartitionOpt.get()); // ENREGISTREMENT
            createdAgents.add(saved); // Ajout à la liste des agents créés
        }

        // Mapping en DTOs
        List<PrimeTsdAgentDto> dtos = new ArrayList<>();
        for (PrimeTsdAgent agent : createdAgents) {
            dtos.add(enDto(agent));
        }
        return dtos;
        
    }
    // Méthode de mapping vers le DTO
    private PrimeTsdAgentDto enDto(PrimeTsdAgent agent) {
        PrimeTsdAgentDto dto = new PrimeTsdAgentDto();
        dto.setIdPrimeAgent(agent.getIdPrimeAgent());
        if (agent.getRAgentRepartition() != null && agent.getRAgentRepartition().getId() != null) {
            dto.setMatricule(agent.getRAgentRepartition().getId().getMatricule());
            dto.setIdRepartition(agent.getRAgentRepartition().getId().getIdRepartition());
        }
        dto.setMontantBonification(agent.getMontantBonification());
        dto.setPartGlobalReparti(agent.getPartGlobalReparti());
        dto.setTotalBrut(agent.getTotalBrut());
        dto.setTotalArrondi(agent.getTotalArrondi());
        dto.setMontantIts(agent.getMontantIts());
        dto.setItsArrondi(agent.getItsArrondi());
        dto.setMontantNetPayer(agent.getMontantNetPayer());
        dto.setMontantNetPayerAr(agent.getMontantNetPayerAr());
        return dto;
    }
}
