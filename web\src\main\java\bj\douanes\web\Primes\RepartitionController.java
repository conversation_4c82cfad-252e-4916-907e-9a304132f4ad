package bj.douanes.web.Primes;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.Model.Repartition;
import bj.douanes.core.shared.response.AppResponse;
import bj.douanes.facade.Prime.RepartitionFacade;
import bj.douanes.facade.UTILS.DTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;


@RestController
@RequiredArgsConstructor
@RequestMapping("api/prime/repartition")
@Tag(name = "Repartition", description = "API for managing repartitions and their associated operations")
public class RepartitionController {
    
    private final RepartitionFacade repartitionFacade;

    //afficher toutes les repartitions
    @GetMapping("/repartitions")
    @Operation(summary = "Get all repartitions", description = "Api pour afficher toutes les repartitions")
    public List<Repartition> getAllRepartitions() {
        return repartitionFacade.getAllRepartitions();
    }  
    
    //par id
    @GetMapping("/{id}")
    @Operation(summary = "Get repartition by ID", description = "Api pour afficher une repartition par son ID")
    public Repartition getRepartitionById(@PathVariable Long id) {
        return repartitionFacade.getRepartitionById(id);
    }

    //creer une nouvelle repartition
    @PostMapping("/createRepartition")
    @Operation(summary = "Create a new repartition", description = "Api pour creer une nouvelle repartition plus complete")
    public ResponseEntity<?> createRepartition(@RequestBody DTO.CreateRepartitionRequest request) {
        return AppResponse.created(repartitionFacade.createRepartition(request.getRepartition(), request.getDtoList()));
    }

    @PutMapping("/update/{id}")
    @Operation(summary = "Update an existing repartition", description = "Api pour mettre a jour les informations une repartition existante")
    public ResponseEntity<?> updateRepartition(@RequestBody DTO.RepartitionDto repartition, @PathVariable Long id) {
        return AppResponse.ok(repartitionFacade.updateRepartition(id, repartition));
    }

    //si possible supprimer une repartition
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "Delete a repartition", description = "Api pour supprimer une repartition")
    public ResponseEntity<?> deleteRepartition(@PathVariable Long id) {
        repartitionFacade.deleteRepartition(id);
        return AppResponse.ok("Repartition deleted successfully");
    }
}
