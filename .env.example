# # ===================================== 
# # FICHIER D'EXEMPLE DES VARIABLES D'ENVIRONNEMENT
# # Copiez ce fichier vers .env et remplissez les valeurs
# # ===================================== 

# # ===================================== CONFIGURATION SERVEUR =====================================
# # Port du serveur (optionnel, défini dans application-{profile}.properties)
# SERVER_PORT=8082

# # Profil Spring actif (dev, prod)
# SPRING_PROFILES_ACTIVE=home

# # ===================================== CONFIGURATION SÉCURITÉ =====================================
# # Liste blanche des endpoints (séparés par des virgules)
# SECURITY_WHITE_LIST=/,/v3/api-docs/**,/swagger-ui/**,/swagger,/doc,/docs,/api/auth/**

# # ===================================== CONFIGURATION JWT =====================================
# # Clé secrète JWT (générez une clé sécurisée de 256 bits minimum)
# JWT_SECRET_KEY=9CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED122C8898D8419CC54945FFFD4FFED122C8898D

# # Durée de validité du token en heures
# JWT_DELAY=24

# # ===================================== CONFIGURATION BASE DE DONNÉES =====================================

# DB_TYPES=dgd,home,test,ORACLE,MYSQL

# # Base de données DGD (Primary)
# DB_DGD_URL=************************************
# DB_DGD_USERNAME=root
# DB_DGD_PASSWORD=your_dgd_password_here

# # Base de données HOME (Secondary)
# DB_HOME_URL=*************************************
# DB_HOME_USERNAME=root
# DB_HOME_PASSWORD=your_home_password_here

# # Base de données TEST (Tertiary)
# DB_TEST_URL=*************************************
# DB_TEST_USERNAME=root
# DB_TEST_PASSWORD=your_test_password_here

# # Base de données Oracle (pour sydoniaplusplus)
# DB_ORACLE_URL=****************************************************************************
# DB_ORACLE_USERNAME=GDEPOT
# DB_ORACLE_PASSWORD=your_oracle_password_here

# # Base de données MySQL (pour dev)
# DB_MYSQL_URL=*****************************************************************************
# DB_MYSQL_USERNAME=root
# DB_MYSQL_PASSWORD=your_mysql_password_here

# # ===================================== CONFIGURATION MAIL =====================================
# # Configuration SMTP
# MAIL_HOST=mail.finances.bj
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your_mail_password_here

# # ===================================== CONFIGURATION DOCKER =====================================
# # Variables pour Docker
# DOCKER_IMAGE_NAME=erp-backend
# DOCKER_IMAGE_TAG=latest
# DOCKER_CONTAINER_NAME=backend
# DOCKER_HOST_PORT=8082