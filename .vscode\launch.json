{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "ERP Backend",
            "request": "launch",
            "mainClass": "bj.douanes.Application",
            "projectName": "web",
            "envFile": "${workspaceFolder}/.env",
            "console": "integratedTerminal",
        },
        {
            "type": "java",
            "name": "ERP Backend (DEBUG)",
            "request": "launch",
            "mainClass": "bj.douanes.Application",
            "projectName": "web",
            "envFile": "${workspaceFolder}/.env",
            "console": "internalConsole",
            "internalConsoleOptions": "openOnSessionStart",
            // "vmArgs": "-Dspring.profiles.active=prod",
        },
    ]
}