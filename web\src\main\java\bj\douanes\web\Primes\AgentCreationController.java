package bj.douanes.web.Primes;

import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.DTO.AgentCreationDto;
import bj.douanes.DTO.UpdateAgentRequest;
import bj.douanes.core.shared.response.AppResponse;
import bj.douanes.facade.Prime.AgentCreationFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@Tag(name = "Agent Creation", description = "API for managing agent creation and related operations")
@RequestMapping("api/prime/agent")
public class AgentCreationController {
    
    private final AgentCreationFacade agentCreationFacade;

    //api pour afficher la liste de tout les agents et leur rattachements et historique
    @GetMapping("/all")
    @Operation(summary = "Get all agents", description = "api pour afficher la liste de tout les agents et leur rattachements et historique")
    public ResponseEntity<?> getAllAgents() {
        return AppResponse.ok(agentCreationFacade.getAllAgents());
    }

    //api pour afficher la liste des agents de la repartition(1)
    @GetMapping("/list/{idRepartition}")
    @Operation(summary = "Get all agents by repartition", description = "api pour afficher la liste des agents de la repartition")
    public ResponseEntity<?> getAllAgentsByRepartition(@PathVariable Long idRepartition) {
        if (idRepartition == null) {
            return AppResponse.badRequest("N° de répartition obligatoire");
        }
        return AppResponse.ok(agentCreationFacade.findListAllAgents(idRepartition));
    }

    //api pour creer un agent et tout ses rattachements (nom,prenom,unite,fonction etc)
    @PostMapping("/create")
    @Operation(summary = "Create a new agent", description = "api pour creer un agent et tout ses rattachements (nom,prenom,unite,fonction etc)")
    public ResponseEntity<?> createAgent(@RequestBody AgentCreationDto agentDetails) {
        agentCreationFacade.createAgent(agentDetails);
        return AppResponse.created("Agent created successfully");
    }

    //api pour mettre a jour un agent et tout ses rattachements (nom,prenom,unite,fonction etc)
    @PutMapping("/update")
    @Operation(summary = "Update an existing agent", description = "api pour mettre a jour un agent et tout ses rattachements (nom,prenom,unite,fonction etc)")
    public ResponseEntity<?> updateAgent(@RequestBody UpdateAgentRequest request) {
        String matricule = request.getMatricule();
        AgentCreationDto agentDetails = request.getAgentDetails();
        if (matricule == null || matricule.isEmpty()) {
            return AppResponse.badRequest("Matricule required");
        }
        agentCreationFacade.updateAgent(matricule, agentDetails);
        return AppResponse.ok("AgentTSD updated successfully");
    }

    //api pour telecharger la liste des agents d'une repartition(2) Exporter (1) vers Excel
    @GetMapping("/download/{idRepartition}")
    @Operation(summary = "Download agent list as Excel", description = "api pour telecharger la liste des agents d'une repartition au format excel")
    public ResponseEntity<byte[]> downloadAgentListOnExcel(@PathVariable Long idRepartition) {
        if (idRepartition == null) {
            return ResponseEntity.badRequest().body(null);
        }
        try {
            byte[] excelData = agentCreationFacade.downloadAgentList(idRepartition);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(
            MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
            headers.setContentDisposition(ContentDisposition.attachment()
                    .filename("agents_repartition_N°" + idRepartition + ".xlsx")
                    .build());
            headers.setContentLength(excelData.length);

            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(null);
        }
    }

    //api pour telecharger la liste des ordres de virement d'une repartition
    @GetMapping("/download/ordre-virement/{idRepartition}")
    @Operation(summary = "Download ordre de virement list as Excel", description = "api pour telecharger la liste des ordres de virement d'une repartition au format excel")
    public ResponseEntity<byte[]> downloadOrdreVirementList(@PathVariable Long idRepartition) {
        if (idRepartition == null) {
            return ResponseEntity.badRequest().body(null);
        }
        try {
            byte[] excelData = agentCreationFacade.downloadOrdreVirementList(idRepartition);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(
                MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
            headers.setContentDisposition(ContentDisposition.attachment()
                    .filename("Ordre_Virement_Repartition_N°" + idRepartition + ".xlsx")
                    .build());
            headers.setContentLength(excelData.length);

            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(null);
        }
    }

    //api pour afficher tout les agent d'une repartition avec leur net a payer
    // @GetMapping("/{idRepartition}")
    // public ResponseEntity<?> getAllAgentsByUnite(@PathVariable Long idRepartition) {
    //     if (idRepartition == null) {
    //         return AppResponse.badRequest("N° de répartition obligatoire");
    //     }
    //     return AppResponse.ok(agentCreationFacade.getAllAgentsByUnite(idRepartition));
    // }
}
