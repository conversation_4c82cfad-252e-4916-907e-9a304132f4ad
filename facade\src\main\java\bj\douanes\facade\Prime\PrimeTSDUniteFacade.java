package bj.douanes.facade.Prime;
import java.util.List;

import org.springframework.stereotype.Service;

import bj.douanes.DTO.PrimeTSDUniteDto;
import bj.douanes.Services.PrimeTSDUniteServ;
import lombok.RequiredArgsConstructor;


public interface PrimeTSDUniteFacade {
    List<PrimeTSDUniteDto> findAllByIdRepartition(Long idRepartition);
    // List<PrimeTSDUnite> createVersementByUnite(List<MontantCollectParUniteDto> dto, Long idRepartition);
    // List<PrimeTSDDTO> findAllPrimes();
    // PrimeTSDDTO getPrimeTSDById(Long id);
}

@Service
@RequiredArgsConstructor
class InnerPrimeTSDUniteFacade implements PrimeTSDUniteFacade {

    private final PrimeTSDUniteServ primeTSDUniteServ;  
    // private final UniteRepartitionEtPrimeServ uniteRepartitionEtPrimeServ;

    @Override
    public List<PrimeTSDUniteDto> findAllByIdRepartition(Long idRepartition) {
        return primeTSDUniteServ.findAllByCodeUniteAndIdRepartition(idRepartition);
    }
    // @Override
    // public List<PrimeTSDUnite> createVersementByUnite(List<MontantCollectParUniteDto> dto, Long idRepartition) {
    //     return uniteRepartitionEtPrimeServ.createVersementUnites(dto, idRepartition);
    // }

    // @Override
    // public List<PrimeTSDDTO> findAllPrimes() {
    //     return uniteRepartitionEtPrimeServ.findAllPrime();
    // }

    // @Override
    // public PrimeTSDDTO getPrimeTSDById(Long id) {
    //     return uniteRepartitionEtPrimeServ.getPrimeTSDById(id);
    // }

}
