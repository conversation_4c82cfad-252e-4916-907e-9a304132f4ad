package bj.douanes.DTO;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrdreVirementDto {

    private String matricule;
    private String nom;
    private String prenoms;
    private String codeBanque;
    private String rib;
    private String nomBanque;  
    private String periode;
    private String annee;
    private String typePrime;
    private BigDecimal montantNetPayer;
    private BigDecimal montantNetPayerAr;
    
}
