package bj.douanes.web.Primes;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.core.shared.response.AppResponse;
import bj.douanes.facade.Prime.EmailFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("api/prime/email")
@Tag(name = "Email", description = "API for sending emails related to agent operations")
public class EmailController {

    private final EmailFacade emailFacade;

    @PostMapping("/{idRepartition}")
    @Operation(summary = "Send ordre de virement email to all agents", description = "API to send ordre de virement email to all agents in a specific repartition")
    public ResponseEntity<?> sendOrdreVirementEmailToAllAgents(@PathVariable Long idRepartition) {
        emailFacade.sendOrdreVirementEmailToAllAgents(idRepartition);
        return AppResponse.ok("Emails sent successfully");
    }
    
}
