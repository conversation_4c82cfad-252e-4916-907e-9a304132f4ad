# Configuration des Variables d'Environnement

Ce document explique comment configurer et utiliser les variables d'environnement pour l'application ERP Douanes.

## 📋 Fichiers de Configuration

### `.env.example`
Fichier modèle contenant toutes les variables d'environnement nécessaires avec des exemples de valeurs.

### `.env`
Fichier contenant les valeurs réelles des variables d'environnement pour votre environnement local.

⚠️ **Important** : Le fichier `.env` est ignoré par Git pour des raisons de sécurité.

## 🚀 Configuration Initiale

### 1. Copier le fichier d'exemple
```bash
cp .env.example .env
```

### 2. Modifier les valeurs dans `.env`
Éditez le fichier `.env` et remplacez les valeurs par défaut par vos configurations réelles :

```bash
# Exemple de configuration pour le développement local
DB_DGD_PASSWORD=votre_mot_de_passe_dgd
DB_HOME_PASSWORD=votre_mot_de_passe_home
DB_TEST_PASSWORD=votre_mot_de_passe_test
MAIL_PASSWORD=votre_mot_de_passe_mail
JWT_SECRET_KEY=votre_cle_secrete_jwt_256_bits
```

## 🔧 Variables d'Environnement Principales

### Base de Données
- `DB_DGD_USERNAME` / `DB_DGD_PASSWORD` : Credentials pour la base DGD
- `DB_HOME_USERNAME` / `DB_HOME_PASSWORD` : Credentials pour la base HOME
- `DB_TEST_USERNAME` / `DB_TEST_PASSWORD` : Credentials pour la base TEST
- `DB_ORACLE_USERNAME` / `DB_ORACLE_PASSWORD` : Credentials pour Oracle (Sydonia++)

### JWT
- `JWT_SECRET_KEY` : Clé secrète pour signer les tokens JWT (256 bits minimum)
- `JWT_DELAY` : Durée de validité des tokens en heures (défaut: 24)

### Mail
- `MAIL_USERNAME` / `MAIL_PASSWORD` : Credentials SMTP
- `MAIL_HOST` : Configuration serveur SMTP

### Sécurité
- `SECURITY_WHITE_LIST` : Endpoints publics (séparés par des virgules)

## 🐳 Utilisation avec Docker

### Docker Run
```bash
docker run -d \
  --env-file .env \
  -p 8082:80 \
  --name backend \
  erp-backend:latest
```

### Docker Compose
```bash
# Démarrage avec les bases de données
docker-compose up -d

# Démarrage de l'application uniquement
docker-compose up erp-backend

# Avec pgAdmin pour la gestion des BDD
docker-compose --profile tools up -d
```

## 🔄 Déploiement GitLab CI/CD

### Variables GitLab CI
Configurez ces variables dans GitLab CI/CD Settings > Variables :

#### Variables de Production
- `DB_DGD_USERNAME` / `DB_DGD_PASSWORD`
- `DB_HOME_USERNAME` / `DB_HOME_PASSWORD`
- `DB_TEST_USERNAME` / `DB_TEST_PASSWORD`
- `JWT_SECRET_KEY`
- `MAIL_USERNAME` / `MAIL_PASSWORD`

#### Variables d'URL (si différentes de localhost)
- `DB_DGD_URL`
- `DB_HOME_URL`
- `DB_TEST_URL`

### Masquage des Variables Sensibles
⚠️ **Important** : Marquez les variables sensibles comme "Masked" dans GitLab CI :
- Tous les mots de passe
- JWT_SECRET_KEY
- Credentials de mail

## 🛠️ Développement Local

### Avec IDE (IntelliJ/VSCode)
1. Configurez les variables d'environnement dans votre IDE
2. Ou utilisez le plugin EnvFile pour charger automatiquement le fichier `.env`

### Avec Maven
```bash
# Chargement automatique du fichier .env (si plugin configuré)
mvn spring-boot:run

# Ou spécification manuelle
mvn spring-boot:run -Dspring-boot.run.arguments="--spring.profiles.active=home"
```

## 🔍 Vérification de la Configuration

### Logs de Démarrage
Vérifiez les logs au démarrage pour confirmer que les variables sont bien chargées :

```bash
# Docker
docker logs backend

# Local
tail -f logs/application.log
```

### Endpoints de Santé
```bash
# Vérification de l'état de l'application
curl http://localhost:8082/actuator/health

# Informations sur l'environnement (dev uniquement)
curl http://localhost:8082/actuator/env
```

## 🔒 Bonnes Pratiques de Sécurité

1. **Ne jamais committer le fichier `.env`**
2. **Utiliser des mots de passe forts** (minimum 12 caractères)
3. **Générer une nouvelle clé JWT** pour chaque environnement
4. **Changer les mots de passe par défaut** en production
5. **Utiliser des secrets managers** en production (HashiCorp Vault, AWS Secrets Manager, etc.)

## 🆘 Dépannage

### Problème : Variables non chargées
- Vérifiez que le fichier `.env` existe
- Vérifiez la syntaxe (pas d'espaces autour du `=`)
- Redémarrez l'application

### Problème : Connexion base de données
- Vérifiez les credentials dans `.env`
- Vérifiez que les bases de données sont démarrées
- Vérifiez les URLs de connexion

### Problème : JWT invalide
- Vérifiez que `JWT_SECRET_KEY` est définie
- Vérifiez que la clé fait au minimum 256 bits
- Régénérez une nouvelle clé si nécessaire

## 📞 Support

Pour toute question sur la configuration, contactez l'équipe de développement ou consultez la documentation technique dans le dossier `docs/`.
